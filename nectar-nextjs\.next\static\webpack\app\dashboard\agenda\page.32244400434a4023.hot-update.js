"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/FullCalendarView.tsx":
/*!*********************************************!*\
  !*** ./src/components/FullCalendarView.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fullcalendar/react */ \"(app-pages-browser)/./node_modules/@fullcalendar/react/dist/index.js\");\n/* harmony import */ var _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fullcalendar/daygrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/daygrid/index.js\");\n/* harmony import */ var _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fullcalendar/timegrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/timegrid/index.js\");\n/* harmony import */ var _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fullcalendar/interaction */ \"(app-pages-browser)/./node_modules/@fullcalendar/interaction/index.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Move getStatusColor outside the component to avoid initialization issues\nconst getStatusColor = (status)=>{\n    const colors = {\n        'scheduled': '#3b82f6',\n        'confirmed': '#10b981',\n        'in_progress': '#f59e0b',\n        'completed': '#6b7280',\n        'cancelled': '#ef4444'\n    };\n    return colors[status] || '#6b7280';\n};\nconst FullCalendarView = (param)=>{\n    let { appointments, healthcareProfessionals, onAppointmentCreate, onAppointmentClick, onAppointmentUpdate, loading = false } = param;\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedProfessional, setSelectedProfessional] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('timeGridWeek');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Initialize component after data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullCalendarView.useEffect\": ()=>{\n            if (!loading && appointments && healthcareProfessionals) {\n                setIsInitialized(true);\n            }\n        }\n    }[\"FullCalendarView.useEffect\"], [\n        loading,\n        appointments,\n        healthcareProfessionals\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullCalendarView.useEffect\": ()=>{\n            const checkMobile = {\n                \"FullCalendarView.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    // Auto-switch to day view on mobile\n                    if (window.innerWidth < 768 && currentView === 'timeGridWeek') {\n                        setCurrentView('timeGridDay');\n                    }\n                }\n            }[\"FullCalendarView.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"FullCalendarView.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"FullCalendarView.useEffect\"];\n        }\n    }[\"FullCalendarView.useEffect\"], [\n        currentView\n    ]);\n    // Filter appointments by selected healthcare professional\n    const filteredAppointments = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[filteredAppointments]\": ()=>{\n            if (selectedProfessional === 'all') {\n                return appointments;\n            }\n            return appointments.filter({\n                \"FullCalendarView.useMemo[filteredAppointments]\": (apt)=>apt.healthcare_professional_id === selectedProfessional\n            }[\"FullCalendarView.useMemo[filteredAppointments]\"]);\n        }\n    }[\"FullCalendarView.useMemo[filteredAppointments]\"], [\n        appointments,\n        selectedProfessional\n    ]);\n    // Convert appointments to FullCalendar events\n    const events = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[events]\": ()=>{\n            return filteredAppointments.map({\n                \"FullCalendarView.useMemo[events]\": (appointment)=>({\n                        id: appointment.id,\n                        title: appointment.title,\n                        start: appointment.start_time,\n                        end: appointment.end_time,\n                        backgroundColor: getStatusColor(appointment.status),\n                        borderColor: getStatusColor(appointment.status),\n                        textColor: '#ffffff',\n                        extendedProps: {\n                            appointment,\n                            description: appointment.description,\n                            patientName: appointment.patient_name,\n                            professionalName: appointment.healthcare_professional_name,\n                            status: appointment.status,\n                            type: appointment.type,\n                            price: appointment.price\n                        }\n                    })\n            }[\"FullCalendarView.useMemo[events]\"]);\n        }\n    }[\"FullCalendarView.useMemo[events]\"], [\n        filteredAppointments\n    ]);\n    const handleDateSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleDateSelect]\": (selectInfo)=>{\n            if (onAppointmentCreate) {\n                onAppointmentCreate(selectInfo);\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleDateSelect]\"], [\n        onAppointmentCreate\n    ]);\n    const handleEventClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleEventClick]\": (clickInfo)=>{\n            const appointment = clickInfo.event.extendedProps.appointment;\n            if (onAppointmentClick && appointment) {\n                onAppointmentClick(appointment);\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleEventClick]\"], [\n        onAppointmentClick\n    ]);\n    const handleEventDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleEventDrop]\": async (dropInfo)=>{\n            try {\n                const appointmentId = dropInfo.event.id;\n                const newStart = dropInfo.event.start;\n                const newEnd = dropInfo.event.end;\n                if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {\n                    dropInfo.revert();\n                    return;\n                }\n                await onAppointmentUpdate(appointmentId, newStart, newEnd);\n                toast({\n                    title: \"Sucesso!\",\n                    description: \"Consulta reagendada com sucesso.\"\n                });\n            } catch (error) {\n                console.error('Error updating appointment:', error);\n                dropInfo.revert();\n                toast({\n                    title: \"Erro\",\n                    description: \"Erro ao reagendar consulta.\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleEventDrop]\"], [\n        onAppointmentUpdate,\n        toast\n    ]);\n    const handleViewChange = (view)=>{\n        var _calendarRef_current;\n        setCurrentView(view);\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.changeView(view);\n        }\n    };\n    const goToToday = ()=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.today();\n        }\n    };\n    const navigateCalendar = (direction)=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            if (direction === 'prev') {\n                calendarApi.prev();\n            } else {\n                calendarApi.next();\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Agenda Completa\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 sm:space-y-0 sm:flex sm:flex-wrap sm:justify-between sm:items-center sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                            value: selectedProfessional,\n                                            onValueChange: setSelectedProfessional,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                    className: \"w-full sm:w-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                        placeholder: \"Filtrar por profissional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"Todos os profissionais\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                value: professional.id,\n                                                                children: [\n                                                                    professional.name,\n                                                                    professional.specialty && \" - \".concat(professional.specialty)\n                                                                ]\n                                                            }, professional.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col xs:flex-row gap-2 xs:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'dayGridMonth' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('dayGridMonth'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'M' : 'Mês'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridWeek' ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>handleViewChange('timeGridWeek'),\n                                                    children: \"Semana\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridDay' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('timeGridDay'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'D' : 'Dia'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('prev'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"‹\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: goToToday,\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"Hoje\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('next'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"›\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fullcalendar-container\",\n                    children: !isInitialized || loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-muted-foreground\",\n                                    children: \"Carregando agenda...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ref: calendarRef,\n                        plugins: [\n                            _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                            _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                        ],\n                        initialView: isMobile ? \"timeGridDay\" : \"timeGridWeek\",\n                        headerToolbar: false,\n                        height: \"auto\",\n                        contentHeight: isMobile ? 400 : \"auto\",\n                        events: events || [],\n                        selectable: true,\n                        selectMirror: true,\n                        editable: !isMobile,\n                        droppable: !isMobile,\n                        eventResizableFromStart: !isMobile,\n                        select: handleDateSelect,\n                        eventClick: handleEventClick,\n                        eventDrop: handleEventDrop,\n                        slotMinTime: \"06:00:00\",\n                        slotMaxTime: \"22:00:00\",\n                        slotDuration: \"00:30:00\",\n                        slotLabelInterval: isMobile ? \"02:00:00\" : \"01:00:00\",\n                        allDaySlot: false,\n                        nowIndicator: true,\n                        businessHours: {\n                            daysOfWeek: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ],\n                            startTime: '08:00',\n                            endTime: '18:00'\n                        },\n                        eventDisplay: \"block\",\n                        dayMaxEvents: isMobile ? 3 : true,\n                        moreLinkClick: \"popover\",\n                        // Mobile-specific settings\n                        aspectRatio: isMobile ? 1.2 : 1.35,\n                        handleWindowResize: true,\n                        stickyHeaderDates: !isMobile,\n                        // Locale configuration - Fixed to use string format\n                        locale: \"pt-br\",\n                        buttonText: {\n                            today: 'Hoje',\n                            month: 'Mês',\n                            week: 'Semana',\n                            day: 'Dia',\n                            list: 'Lista'\n                        },\n                        weekText: \"Sm\",\n                        allDayText: \"Todo o dia\",\n                        moreLinkText: \"mais\",\n                        noEventsText: \"N\\xe3o h\\xe1 eventos para mostrar\",\n                        firstDay: 0,\n                        eventContent: (eventInfo)=>{\n                            var _eventInfo_event_extendedProps, _eventInfo_event_extendedProps1;\n                            const title = eventInfo.event.title || '';\n                            const patientName = (_eventInfo_event_extendedProps = eventInfo.event.extendedProps) === null || _eventInfo_event_extendedProps === void 0 ? void 0 : _eventInfo_event_extendedProps.patientName;\n                            const professionalName = (_eventInfo_event_extendedProps1 = eventInfo.event.extendedProps) === null || _eventInfo_event_extendedProps1 === void 0 ? void 0 : _eventInfo_event_extendedProps1.professionalName;\n                            const startTime = eventInfo.event.start;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-1 \".concat(isMobile ? 'text-xs' : 'text-xs'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium truncate text-xs sm:text-sm\",\n                                        children: isMobile && title.length > 20 ? title.substring(0, 20) + '...' : title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    patientName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-90 truncate\",\n                                        children: patientName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    professionalName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: professionalName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    isMobile && startTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(startTime, 'HH:mm')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 23\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 19\n                            }, void 0);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FullCalendarView, \"rkLhhgpT0lsjBttF3RmzTu/JmYA=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = FullCalendarView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FullCalendarView);\nvar _c;\n$RefreshReg$(_c, \"FullCalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FullCalendarView.tsx\n"));

/***/ })

});